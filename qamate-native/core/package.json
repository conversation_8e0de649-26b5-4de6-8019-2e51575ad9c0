{"private": true, "version": "3.0.31", "name": "QAMate", "description": "QAMate1.0", "main": "./src/main.js", "scripts": {"dev": "echo '{\"env\": \"test\", \"mode\": \"localtool\"}' > ./env.json && cross-env NODE_ENV=dev electron .", "dev-cmd": "echo '{\"env\": \"test\", \"mode\": \"cmd\"}' > ./env.json && cross-env NODE_ENV=dev node ./src/main.js", "dev-online": "echo '{\"env\": \"test\", \"mode\": \"localtool\"}' > ./env.json && cross-env NODE_ENV=dev-online electron .", "dev-preview": "echo '{\"env\": \"test\", \"mode\": \"localtool\"}' > ./env.json && cross-env NODE_ENV=dev-preview electron .", "pre-product": "echo '{\"env\": \"product\", \"mode\": \"localtool\"}' > ./env.json && cp ./build/icon_product.png ./build/icon.png", "pre-preview": "echo '{\"env\": \"preview\", \"mode\": \"localtool\"}' > ./env.json && cp ./build/icon_preview.png ./build/icon.png", "pre-test": "echo '{\"env\": \"test\", \"mode\": \"localtool\"}' > ./env.json && cp ./build/icon_test.png ./build/icon.png", "build-qamate": "sh scripts/build-qamate.sh", "build-qamate-without-server": "sh scripts/build-qamate.sh --no-need-strategy --no-need-agent", "build-qamate-without-model": "sh scripts/build-qamate.sh --no-need-model --no-need-agent", "build-qamate-without-model-but-agent": "sh scripts/build-qamate.sh --no-need-model", "build-qamate-with-agent": "sh scripts/build-qamate.sh --no-need-strategy", "upload-pack": "node scripts/upload-pack.js", "upload-yaml": "node scripts/upload-yaml.js", "upload-server": "node scripts/upload-server.js", "upload-agent": "node scripts/upload-agent.js"}, "dependencies": {"@baidu/anyproxy": "^5.0.5", "@baidu/bat-automator": "2.1.207", "@baidu/bat-util": "^8.0.78", "@baiducloud/sdk": "1.0.0-rc.38", "axios": "1.3.4", "cacheable-lookup": "^6.0.4", "cookie": "^1.0.2", "decimal-eval": "^0.1.1", "electron-updater": "5.3.0", "expr-eval": "^2.0.2", "get-port": "^5.1.1", "jsonpath": "^1.1.1", "object-sizeof": "^2.6.1", "sharp": "^0.30.7", "sqlite3": "5.1.6", "url-parse": "^1.5.10", "uuid": "^9.0.0", "ws": "^8.16.0", "yaml": "^2.2.1"}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^34.2.0", "electron-builder": "23.6.0", "electron-packager": "15.5.2", "electron-rebuild": "^3.2.9"}, "build": {"mac": {"hardenedRuntime": false, "entitlements": "build/entitlements.mac.plist", "extendInfo": {"NSCameraUsageDescription": "设备录入需要使用摄像头权限, 球球了", "com.apple.security.device.camera": true, "NSMicrophoneUsageDescription": "设备录入需要使用麦克风权限, 球球了", "com.apple.security.device.audio-input": true}, "signIgnore": ["/public/server/", "/public/model/", "/public/agent/"]}, "electronDownload": {"mirror": "https://registry.npmmirror.com/-/binary/electron/"}, "extraResources": ["./public/certificate/**", "./public/bat-wda/**", "./public/hot-update/**", "./public/server/**", "./public/model/**", "./public/agent/**"], "publish": {"provider": "generic", "url": ""}}}