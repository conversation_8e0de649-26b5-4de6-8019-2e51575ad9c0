{"name": "@baidu/bat-automator", "description": "bat-automator", "version": "2.1.192", "main": "./src/index.js", "scripts": {"lint": "eslint ./src", "test": "mocha ./test/*", "testAndroid": "mocha ./test/android/*", "testIOS": "mocha ./test/iOS/*"}, "husky": {"hooks": {"pre-commit": "npm run lint"}}, "dependencies": {"@baidu/bat-util": "^8.0.70", "automator": "^0.1.0", "axios": "^0.20.0", "bat-adbkit": "^2.11.12", "get-port": "^5.1.1", "sharp": "^0.30.7", "tree-kill": "^1.2.2", "url-parse": "^1.5.10", "uuid": "^9.0.0", "xml-js": "^1.6.11", "xmldom": "^0.3.0", "xpath": "^0.0.27"}, "devDependencies": {"eslint": "^6.8.0", "mocha": "^10.0.0"}, "author": "longbatian"}