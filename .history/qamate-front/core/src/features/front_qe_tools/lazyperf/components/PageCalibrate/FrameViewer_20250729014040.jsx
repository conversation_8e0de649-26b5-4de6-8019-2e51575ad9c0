import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Image, Tooltip, message, Button, Slider, Tag } from 'antd';
import {
    CaretLeftFilled,
    CaretRightFilled,
    EyeOutlined,
    RotateLeftOutlined,
    RotateRightOutlined
} from '@ant-design/icons';
import styles from './FrameViewer.module.less';

const FrameViewer = ({ record, onFrameCorrect, onSwitchToPrevRecord, onSwitchToNextRecord }) => {
    const [firstPage, setFirstPage] = useState(0);
    const [lastPage, setLastPage] = useState(0);
    const [activeSection, setActiveSection] = useState('first'); // 'first' or 'last'
    const [previewIndex, setPreviewIndex] = useState(-1);
    const [framePerPage, setFramePerPage] = useState(5);
    const [frameWidth, setFrameWidth] = useState(150);
    const containerRef = useRef(null);
    const frames = record?.frameList || [];
    const totalFrames = frames.length;
    const stageList = record?.stageList || [];

    // 自适应容器宽度，动态计算每页显示的帧数和帧宽度
    useEffect(() => {
        const calculateLayout = () => {
            if (!containerRef.current) return;

            const containerWidth = containerRef.current.offsetWidth;
            const controlWidth = 80; // 左右控制按钮的总宽度
            const availableWidth = containerWidth - controlWidth;
            const minFrameWidth = 120; // 最小帧宽度
            const maxFrameWidth = 200; // 最大帧宽度
            const frameMargin = 10; // 帧之间的间距

            // 计算最佳帧宽度和每页帧数
            let bestFrameWidth = 150;
            let bestFramePerPage = 5;

            // 尝试不同的帧数，找到最佳布局
            for (let frameCount = 3; frameCount <= 8; frameCount++) {
                const totalMargin = frameMargin * frameCount;
                const calculatedFrameWidth = (availableWidth - totalMargin) / frameCount;

                if (calculatedFrameWidth >= minFrameWidth && calculatedFrameWidth <= maxFrameWidth) {
                    bestFrameWidth = Math.floor(calculatedFrameWidth);
                    bestFramePerPage = frameCount;
                }
            }

            setFrameWidth(bestFrameWidth);
            setFramePerPage(bestFramePerPage);
        };

        // 初始计算
        calculateLayout();

        // 监听窗口大小变化
        const resizeObserver = new ResizeObserver(calculateLayout);
        if (containerRef.current) {
            resizeObserver.observe(containerRef.current);
        }

        return () => {
            resizeObserver.disconnect();
        };
    }, []);

    // 初始化页面位置 - 按照旧代码逻辑，页面值就是起始帧索引
    useEffect(() => {
        if (frames.length > 0) {
            const firstFrameIndex = record?.correctDetail?.manual?.firstFrameIndex ||
                                  record?.correctDetail?.auto?.firstFrameIndex || 0;
            const lastFrameIndex = record?.correctDetail?.manual?.lastFrameIndex ||
                                 record?.correctDetail?.auto?.lastFrameIndex ||
                                 frames.length - 1;

            // 按照旧代码逻辑，页面值直接是起始帧的索引
            // 为了让目标帧显示在中间位置，我们减去changePage(2)
            const changePage = 2;
            setFirstPage(Math.max(0, firstFrameIndex - changePage));
            setLastPage(Math.max(0, lastFrameIndex - changePage));
        }
    }, [record, frames.length]);

    // 键盘事件处理 - 完全参照旧版本key-down.js逻辑
    const handleKeyDown = useCallback((event) => {
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            return; // 在输入框中不处理键盘事件
        }

        const maxStartFrameIndex = totalFrames - framePerPage - 1;
        const pageNum = Math.floor(framePerPage / 2); // 动态计算翻页的帧数
        const changePage = Math.floor(framePerPage / 2); // 用于转折点计算的偏移量

        // W - 向上切换文件（完全参照旧版本逻辑）
        if (event.keyCode === 87) {
            if (activeSection === 'last') { // 对应旧版本 isFrame === 2
                // 如果当前在尾帧区域，切换到首帧区域
                setActiveSection('first');
            } else { // 对应旧版本 isFrame === 1
                // 如果当前在首帧区域，切换到上一个视频记录
                if (onSwitchToPrevRecord) {
                    onSwitchToPrevRecord();
                }
            }
        }

        // S - 向下切换文件（完全参照旧版本逻辑）
        if (event.keyCode === 83) {
            if (activeSection === 'first') { // 对应旧版本 isFrame === 1
                // 如果当前在首帧区域，切换到尾帧区域
                setActiveSection('last');
            } else { // 对应旧版本 isFrame === 2
                // 如果当前在尾帧区域，切换到下一个视频记录
                if (onSwitchToNextRecord) {
                    onSwitchToNextRecord();
                }
            }
        }

        // A - 帧向左翻页（参照旧版本逻辑）
        if (event.keyCode === 65) {
            if (activeSection === 'first') {
                setFirstPage(prev => Math.max(0, prev - pageNum));
            } else {
                setLastPage(prev => Math.max(0, prev - pageNum));
            }
        }

        // D - 帧向右翻页（参照旧版本逻辑）
        if (event.keyCode === 68) {
            if (activeSection === 'first') {
                setFirstPage(prev => Math.min(maxStartFrameIndex, prev + pageNum));
            } else {
                setLastPage(prev => Math.min(maxStartFrameIndex, prev + pageNum));
            }
        }

        // Escape - 回到人工/智能首尾帧（参照旧版本逻辑）
        if (event.keyCode === 27) {
            resetToMainFrame();
        }

        // Q - 跳转到上一个重要节点（参照旧版本逻辑）
        if (event.keyCode === 81) {
            handleStageJump('prev', changePage);
        }

        // E - 跳转到下一个重要节点（参照旧版本逻辑）
        if (event.keyCode === 69) {
            handleStageJump('next', changePage);
        }
    }, [activeSection, totalFrames, framePerPage, firstPage, lastPage, stageList, onSwitchToPrevRecord, onSwitchToNextRecord]);

    // 绑定键盘事件
    useEffect(() => {
        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [handleKeyDown]);

    // 跳转到转场点 - 完全参照旧版本key-down.js的Q/E键逻辑
    const handleStageJump = (direction, changePage) => {
        // 检查转场点数据是否存在
        if (!stageList || stageList.length === 0) {
            message.info('已无转场趋势点');
            return;
        }

        const currentPage = activeSection === 'first' ? firstPage : lastPage;
        const setPage = activeSection === 'first' ? setFirstPage : setLastPage;

        if (direction === 'prev') {
            // Q键逻辑 - 跳转到上一个重要节点
            let node = 0;
            for (let [index, item] of stageList.entries()) {
                if (item > currentPage) {
                    node = index;
                    break;
                }
            }

            // 最左情况
            if (currentPage < stageList[0]) {
                message.info('已无转场趋势点');
            }
            // 最右情况
            else if (currentPage > stageList[stageList.length - 1]) {
                setPage(stageList[stageList.length - 1] > changePage ?
                    stageList[stageList.length - 1] - changePage : 0);
            }
            // 中部情况
            else {
                setPage(stageList[node - 1] > changePage ?
                    stageList[node - 1] - changePage : 0);
            }
        } else if (direction === 'next') {
            // E键逻辑 - 跳转到下一个重要节点
            let node = stageList.length - 1;
            for (let [index, item] of stageList.entries()) {
                if (index !== stageList.length - 1 && item > currentPage + changePage) {
                    node = index;
                    break;
                }
            }

            // 最右情况
            if (currentPage + changePage >= stageList[stageList.length - 1]) {
                message.info('已无转场趋势点');
            }
            // 最左情况
            else if (currentPage + changePage < stageList[0]) {
                setPage(stageList[0] > changePage ? stageList[0] - changePage : 0);
            }
            // 中部情况
            else {
                setPage(stageList[node] > changePage ? stageList[node] - changePage : 0);
            }
        }
    };

    // 重置到主帧位置
    const resetToMainFrame = () => {
        const firstFrameIndex = record?.correctDetail?.manual?.firstFrameIndex ||
                              record?.correctDetail?.auto?.firstFrameIndex || 0;
        const lastFrameIndex = record?.correctDetail?.manual?.lastFrameIndex ||
                             record?.correctDetail?.auto?.lastFrameIndex ||
                             frames.length - 1;

        // 按照旧代码逻辑，页面值直接是起始帧的索引
        // 为了让目标帧显示在中间位置，我们减去changePage(2)
        const changePage = 2;
        setFirstPage(Math.max(0, firstFrameIndex - changePage));
        setLastPage(Math.max(0, lastFrameIndex - changePage));
    };

    // 双击设置首帧/尾帧
    const handleFrameDoubleClick = (frameIndex, isFirstSection) => {
        if (record?.isValid === 1) {
            message.info('该记录已废弃，无法校准');
            return;
        }

        const frameData = frames[frameIndex];
        if (frameData) {
            const correctionType = isFirstSection ? 'first' : 'last';
            onFrameCorrect?.(frameIndex, frameData.timestamp, correctionType);
        }
    };

    // 渲染帧列表 - 按照旧代码样式
    const renderFrames = (type) => {
        const isFirstSection = type === 'first';
        const currentPage = isFirstSection ? firstPage : lastPage;
        // 按照旧代码逻辑，currentPage就是起始帧的索引
        const startIndex = currentPage;
        const endIndex = Math.min(startIndex + framePerPage, totalFrames);

        const frameElements = [];

        for (let index = startIndex; index < endIndex; index++) {
            const frame = frames[index];
            if (!frame) continue;

            // 确定边框样式和标签 - 完全按照旧代码逻辑
            let border = '1px solid';
            let maskMsg = '';
            let id = '';

            // 转场点标记
            if (stageList.includes(index)) {
                border = '3px dashed orange';
                maskMsg = '转场点';
            }

            // 智能首尾帧标记
            if (isFirstSection) {
                if (index === record?.correctDetail?.auto?.firstFrameIndex) {
                    border = '3px dashed #ff0000';
                    maskMsg = '智能首帧';
                    id = `first-frame-${index}`;
                }
                if (index === record?.correctDetail?.manual?.firstFrameIndex) {
                    border = '3px solid #ff0000';
                    maskMsg = '人工首帧';
                    id = `first-frame-${index}`;
                }
            } else {
                if (index === record?.correctDetail?.auto?.lastFrameIndex) {
                    border = '3px dashed #ff0000';
                    maskMsg = '智能尾帧';
                    id = `last-frame-${index}`;
                }
                if (index === record?.correctDetail?.manual?.lastFrameIndex) {
                    border = '3px solid #ff0000';
                    maskMsg = '人工尾帧';
                    id = `last-frame-${index}`;
                }
            }

            frameElements.push(
                <Tooltip
                    key={index}
                    placement="top"
                    title={
                        <div>
                            <div>index: {index}</div>
                            <div>{maskMsg}</div>
                        </div>
                    }
                >
                    <li
                        id={id}
                        className={styles.frame_li}
                    >


                        <div style={
                            {
                                position: 'relative'
                            }
                        }>
                            <Image
                            placeholder
                            width={frameWidth}
                            height={Math.round(frameWidth * 5/3)} // 保持3:5的宽高比
                            style={{
                                border: border,
                                objectFit: 'cover'
                            }}
                            src={frame.frame}
                            preview={{
                                mask: (
                                    <Button
                                        shape='round'
                                        icon={<EyeOutlined />}
                                        style={{
                                            position: 'absolute',
                                            bottom: 35
                                        }}
                                        onClick={() => setPreviewIndex(index)}
                                    >
                                        预览
                                    </Button>
                                ),
                                visible: previewIndex === index,
                                onVisibleChange: (visible) => {
                                    if (!visible) setPreviewIndex(-1);
                                }
                            }}
                            onClick={() => setActiveSection(type)}
                            onDoubleClick={() => handleFrameDoubleClick(index, isFirstSection)}
                        />

                        {/* 标签 - 按照旧代码样式 */}
                        {maskMsg && (
                            <div
                                style={{
                                    textAlign: 'center',
                                    width: '100%',
                                    zIndex: 998,
                                    background: 'rgba(0, 0, 0, 0.5)',
                                    color: 'white',
                                    position: 'absolute',
                                    bottom: 0
                                }}
                            >
                                {maskMsg}
                            </div>
                        )}
                        </div>

                        {/* 时间戳 - 在图片下方 */}
                        <div style={{
                            display: 'flex',
                            marginTop: 5,
                            justifyContent: 'center',
                        }}>
                            <Tag color="orange">
                            {frame.timestamp - frames[0].timestamp} ms
                        </Tag>
                        </div>
                    </li>
                </Tooltip>
            );
        }

        return frameElements;
    };

    // 渲染左侧控制 - 使用flexbox布局
    const renderLeftControl = (type) => {
        const currentPage = type === 'first' ? firstPage : lastPage;
        const setPage = type === 'first' ? setFirstPage : setLastPage;
        const pageNum = Math.floor(framePerPage / 2); // 动态计算翻页数量

        return (
            <div
                style={{
                    position: 'relative',
                    width: 40,
                    minHeight: 280,
                    flexShrink: 0,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center'
                }}
            >
                {/* 跳转上一个转场点 */}
                {stageList.length > 0 && (
                    <Tooltip title={'跳转上一个转场趋势点'}>
                        <RotateLeftOutlined
                            style={{
                                fontSize: 20,
                                cursor: 'pointer',
                                marginBottom: 10
                            }}
                            onClick={() => jumpToStagePoint(-1)}
                        />
                    </Tooltip>
                )}

                {/* 跳转上页 */}
                <CaretLeftFilled
                    style={{
                        fontSize: 20,
                        cursor: 'pointer'
                    }}
                    onClick={() => {
                        // 按照旧代码逻辑，每次减少pageNum帧
                        setPage(Math.max(0, currentPage - pageNum));
                    }}
                />
            </div>
        );
    };

    // 渲染右侧控制 - 使用flexbox布局
    const renderRightControl = (type) => {
        const currentPage = type === 'first' ? firstPage : lastPage;
        const setPage = type === 'first' ? setFirstPage : setLastPage;
        const pageNum = Math.floor(framePerPage / 2); // 动态计算翻页数量
        const maxStartFrameIndex = totalFrames - framePerPage - 1; // 最后一页的起始帧索引

        return (
            <div
                style={{
                    position: 'relative',
                    width: 40,
                    minHeight: 280,
                    flexShrink: 0,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center'
                }}
            >
                {/* 跳转下一个转场点 */}
                {stageList.length > 0 && (
                    <Tooltip title={'跳转下一个转场趋势点'}>
                        <RotateRightOutlined
                            style={{
                                fontSize: 20,
                                cursor: 'pointer',
                                marginBottom: 10
                            }}
                            onClick={() => jumpToStagePoint(1)}
                        />
                    </Tooltip>
                )}

                {/* 跳转后页 */}
                <CaretRightFilled
                    style={{
                        fontSize: 20,
                        cursor: 'pointer'
                    }}
                    onClick={() => {
                        // 按照旧代码逻辑，每次增加pageNum帧
                        setPage(Math.min(maxStartFrameIndex, currentPage + pageNum));
                    }}
                />
            </div>
        );
    };

    // 渲染进度条 - 完全按照旧代码BarSlider样式
    const renderBarSlider = (type) => {
        const isFirstSection = type === 'first';
        const currentPage = isFirstSection ? firstPage : lastPage;
        const setPage = isFirstSection ? setFirstPage : setLastPage;
        const isActive = activeSection === type;

        // 使用动态的framePerPage
        const pageNum = framePerPage;
        const changePage = Math.floor(framePerPage / 2); // 动态计算changePage，保持相对位置

        // 转场点标记 - 根据动态framePerPage调整
        const marks = {};
        if (stageList && stageList.length > 0) {
            for (let stage of stageList) {
                // 确保转折点在滚动条范围内
                const markPosition = stage - changePage;
                if (markPosition >= 0 && markPosition <= totalFrames - pageNum - 1) {
                    marks[markPosition] = ' ';
                }
            }
        }

        // 按照旧代码：value是当前显示的第一帧的索引，max是总帧数减去每页帧数再减1
        // 这样滑动条就可以一张张滑动了
        const currentStartFrameIndex = currentPage; // 当前显示的第一帧索引
        const maxStartFrameIndex = totalFrames - pageNum - 1; // 最后一页的第一帧索引

        return (
            <div
                style={{
                    width: '80%',
                    margin: '10px auto',
                    padding: '10px 5px'
                }}
                onClick={() => setActiveSection(type)}
            >
                <Slider
                    marks={marks}
                    disabled={!isActive}
                    min={0}
                    max={Math.max(0, maxStartFrameIndex)}
                    value={currentStartFrameIndex}
                    onChange={(frameIndex) => {
                        // frameIndex是要显示的第一帧的索引
                        // 我们需要将其转换为页面索引
                        setPage(frameIndex);
                    }}
                />
            </div>
        );
    };

    if (!frames.length) {
        return (
            <div style={{ width: '100%', marginTop: -10, backgroundColor: '#fff' }}>
                <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
                    暂无帧数据
                </div>
            </div>
        );
    }

    return (
        <div
            ref={containerRef}
            id={'check-correct-list'}
            style={{ width: '100%', marginTop: -10, backgroundColor: '#fff' }}
        >
            {/* 首帧区域 - 使用flexbox布局 */}
            {frames.length > 0 && (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        width: '100%',
                        minHeight: 280,
                        marginTop: 0
                    }}
                >
                    {renderLeftControl('first')}
                    <div style={{ flex: 1, minWidth: 0 }}>
                        <ul
                            style={{
                                width: '100%',
                                padding: 0,
                                margin: 0,
                                opacity: activeSection === 'last' ? 0.2 : 1,
                                display: 'flex',
                                flexWrap: 'wrap',
                                justifyContent: 'flex-start',
                                listStyle: 'none'
                            }}
                        >
                            {renderFrames('first')}
                        </ul>
                    </div>
                    {renderRightControl('first')}
                </div>
            )}

            {/* 首帧进度条 */}
            {frames.length > 0 && renderBarSlider('first')}

            {/* 尾帧区域 - 使用flexbox布局 */}
            {frames.length > 0 && (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        width: '100%',
                        minHeight: 280,
                        marginTop: 3
                    }}
                >
                    {renderLeftControl('last')}
                    <div style={{ flex: 1, minWidth: 0 }}>
                        <ul
                            style={{
                                width: '100%',
                                padding: 0,
                                margin: 0,
                                opacity: activeSection === 'first' ? 0.2 : 1,
                                display: 'flex',
                                flexWrap: 'wrap',
                                justifyContent: 'flex-start',
                                listStyle: 'none'
                            }}
                        >
                            {renderFrames('last')}
                        </ul>
                    </div>
                    {renderRightControl('last')}
                </div>
            )}

            {/* 尾帧进度条 */}
            {frames.length > 0 && renderBarSlider('last')}
        </div>
    );
};

export default FrameViewer;
