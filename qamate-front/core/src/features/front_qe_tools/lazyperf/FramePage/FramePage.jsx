import { useEffect, useState } from 'react';
import { getSpeedRoundList } from 'COMMON/api/front_qe_tools/lazyperf';
import CalibrateTable from './components/CalibrateTable';
import Operate from '../components/Operate';
import styles from './FramePage.module.less';

const FramePage = (props) => {
    const { curTask } = props;
    const [curCaseNodeId, setCurCaseNodeId] = useState(null);
    const [curSceneId, setCurSceneId] = useState(null);
    const [sceneList, setSceneList] = useState([]);

    console.log('curTask', curTask);

    const caseNodeOptions = (curTask?.planParams?.caseNodeParams?.caseNodeList || []).map(
        (item) => ({
            value: item?.caseNodeId,
            key: item?.caseNodeId,
            label: item?.caseNodeName
        })
    );

    const sceneListOptions = (sceneList || []).map((item) => ({
        value: item?.id,
        key: item?.id,
        label: item?.name
    }));

    useEffect(() => {
        console.log('curCaseNode', curCaseNodeId);
        console.log('caseNodeList', curTask?.planParams?.caseNodeParams?.caseNodeList);
        const curCaseNode = curTask?.planParams?.caseNodeParams?.caseNodeList?.find(
            (item) => item?.caseNodeId === curCaseNodeId
        );
        console.log('curCaseNode', curCaseNode);
        setSceneList(curCaseNode?.sceneList || []);
    }, [curCaseNodeId]);

    useEffect(() => {
        async function func() {
            if (!curTask) {
                return;
            }
            let res = await getSpeedRoundList({
                planId: curTask?.planId,
                caseNodeId: curCaseNodeId,
                sceneId: curSceneId
            });
            console.log('res', res);
        }
        func();
    }, [curSceneId, curCaseNodeId]);

    console.log('curSceneId', curSceneId);

    // Mock数据 - 模拟真实的性能测试场景
    const recordList = [
        {
            recordSceneId: 123,
            isUpload: 1, // 已上传
            frameList: [
                { timestamp: 1752762044, frame: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=300&h=200&fit=crop' }, // 启动画面
                { timestamp: 1752762144, frame: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=300&h=200&fit=crop' }, // 加载中
                { timestamp: 1752762244, frame: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=300&h=200&fit=crop' }, // 首帧 - 主界面出现
                { timestamp: 1752762344, frame: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop' },
                { timestamp: 1752762444, frame: 'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=300&h=200&fit=crop' },
                { timestamp: 1752762544, frame: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=300&h=200&fit=crop' },
                { timestamp: 1752762644, frame: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=300&h=200&fit=crop' },
                { timestamp: 1752762744, frame: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=300&h=200&fit=crop' }, // 转场点1
                { timestamp: 1752762844, frame: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=300&h=200&fit=crop' },
                { timestamp: 1752762944, frame: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop' },
                { timestamp: 1752763044, frame: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop' },
                { timestamp: 1752763144, frame: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=200&fit=crop' },
                { timestamp: 1752763244, frame: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=300&h=200&fit=crop' }, // 转场点2
                { timestamp: 1752763344, frame: 'https://images.unsplash.com/photo-1515879218367-8466d910aaa4?w=300&h=200&fit=crop' },
                { timestamp: 1752763444, frame: 'https://images.unsplash.com/photo-1550745165-9bc0b252726f?w=300&h=200&fit=crop' },
                { timestamp: 1752763544, frame: 'https://images.unsplash.com/photo-1542831371-29b0f74f9713?w=300&h=200&fit=crop' }, // 转场点3
                { timestamp: 1752763644, frame: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop' },
                { timestamp: 1752763744, frame: 'https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=300&h=200&fit=crop' },
                { timestamp: 1752763844, frame: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=300&h=200&fit=crop' }, // 尾帧 - 完全加载完成
                { timestamp: 1752763944, frame: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=300&h=200&fit=crop' }
            ],
            stepRecord: [
                [
                    { name: '点击启动按钮', timestamp: 1752762044 },
                    { name: '应用启动', timestamp: 1752762144 },
                    { name: '主界面渲染', timestamp: 1752762244 }
                ],
                [
                    { name: '数据加载完成', timestamp: 1752763844 }
                ]
            ],
            stageList: [7, 12, 15], // 转场点索引
            correctDetail: {
                auto: {
                    firstFrameStatus: 0, // 智能识别但未人工确认
                    firstFrameIndex: 2, // 智能识别的首帧
                    firstFrameTimestamp: 1752762244,
                    lastFrameStatus: 0,
                    lastFrameIndex: 18, // 智能识别的尾帧
                    lastFrameTimestamp: 1752763844
                },
                manual: {
                    firstFrameStatus: 1, // 已人工校准
                    firstFrameIndex: 2, // 人工确认的首帧
                    firstFrameTimestamp: 1752762244,
                    lastFrameStatus: 1, // 已人工校准
                    lastFrameIndex: 18, // 人工确认的尾帧
                    lastFrameTimestamp: 1752763844
                }
            },
            isValid: 0, // 有效记录
            invalidComment: '',
            updateUser: '<EMAIL>',
            updateTime: 1752764000
        },
        {
            recordSceneId: 124,
            isUpload: 1,
            frameList: [
                { timestamp: 1752762000, frame: 'https://images.unsplash.com/photo-1607706189992-eae578626c86?w=300&h=200&fit=crop' }, // 黑屏
                { timestamp: 1752762100, frame: 'https://images.unsplash.com/photo-1593720213428-28a5b9e94613?w=300&h=200&fit=crop' }, // 启动logo
                { timestamp: 1752762200, frame: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=300&h=200&fit=crop' },
                { timestamp: 1752762300, frame: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop' },
                { timestamp: 1752762400, frame: 'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=300&h=200&fit=crop' }, // 首帧候选
                { timestamp: 1752762500, frame: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=300&h=200&fit=crop' }, // 转场点1
                { timestamp: 1752762600, frame: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=300&h=200&fit=crop' },
                { timestamp: 1752762700, frame: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=300&h=200&fit=crop' },
                { timestamp: 1752762800, frame: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=300&h=200&fit=crop' },
                { timestamp: 1752762900, frame: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop' }, // 转场点2
                { timestamp: 1752763000, frame: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop' },
                { timestamp: 1752763100, frame: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=200&fit=crop' },
                { timestamp: 1752763200, frame: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=300&h=200&fit=crop' },
                { timestamp: 1752763300, frame: 'https://images.unsplash.com/photo-1515879218367-8466d910aaa4?w=300&h=200&fit=crop' },
                { timestamp: 1752763400, frame: 'https://images.unsplash.com/photo-1550745165-9bc0b252726f?w=300&h=200&fit=crop' }, // 转场点3
                { timestamp: 1752763500, frame: 'https://images.unsplash.com/photo-1542831371-29b0f74f9713?w=300&h=200&fit=crop' },
                { timestamp: 1752763600, frame: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop' },
                { timestamp: 1752763700, frame: 'https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=300&h=200&fit=crop' },
                { timestamp: 1752763800, frame: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=300&h=200&fit=crop' },
                { timestamp: 1752763900, frame: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=300&h=200&fit=crop' }, // 转场点4
                { timestamp: 1752764000, frame: 'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=300&h=200&fit=crop' },
                { timestamp: 1752764100, frame: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=300&h=200&fit=crop' },
                { timestamp: 1752764200, frame: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=300&h=200&fit=crop' }, // 尾帧
                { timestamp: 1752764300, frame: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=300&h=200&fit=crop' },
                { timestamp: 1752764400, frame: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=300&h=200&fit=crop' }
            ],
            stepRecord: [
                [
                    { name: '冷启动', timestamp: 1752762000 },
                    { name: '显示启动logo', timestamp: 1752762100 },
                    { name: '主界面开始渲染', timestamp: 1752762400 }
                ],
                [
                    { name: '所有内容加载完毕', timestamp: 1752764200 }
                ]
            ],
            stageList: [5, 9, 14, 19], // 转场点索引
            correctDetail: {
                auto: {
                    firstFrameStatus: 0,
                    firstFrameIndex: 4, // 智能识别首帧
                    firstFrameTimestamp: 1752762400,
                    lastFrameStatus: 0,
                    lastFrameIndex: 22, // 智能识别尾帧
                    lastFrameTimestamp: 1752764200
                },
                manual: {
                    firstFrameStatus: 0, // 未人工校准
                    firstFrameIndex: 4,
                    firstFrameTimestamp: 1752762400,
                    lastFrameStatus: 0, // 未人工校准
                    lastFrameIndex: 22,
                    lastFrameTimestamp: 1752764200
                }
            },
            isValid: 0,
            invalidComment: '',
            updateUser: '<EMAIL>',
            updateTime: 1752764500
        },
        {
            recordSceneId: 125,
            isUpload: 0, // 未上传
            frameList: [
                { timestamp: 1752761000, frame: 'https://images.unsplash.com/photo-1607706189992-eae578626c86?w=300&h=200&fit=crop' },
                { timestamp: 1752761100, frame: 'https://images.unsplash.com/photo-1593720213428-28a5b9e94613?w=300&h=200&fit=crop' },
                { timestamp: 1752761200, frame: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=300&h=200&fit=crop' },
                { timestamp: 1752761300, frame: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop' },
                { timestamp: 1752761400, frame: 'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=300&h=200&fit=crop' },
                { timestamp: 1752761500, frame: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=300&h=200&fit=crop' },
                { timestamp: 1752761600, frame: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=300&h=200&fit=crop' },
                { timestamp: 1752761700, frame: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=300&h=200&fit=crop' },
                { timestamp: 1752761800, frame: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=300&h=200&fit=crop' },
                { timestamp: 1752761900, frame: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop' },
                { timestamp: 1752762000, frame: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop' },
                { timestamp: 1752762100, frame: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=200&fit=crop' },
                { timestamp: 1752762200, frame: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=300&h=200&fit=crop' },
                { timestamp: 1752762300, frame: 'https://images.unsplash.com/photo-1515879218367-8466d910aaa4?w=300&h=200&fit=crop' },
                { timestamp: 1752762400, frame: 'https://images.unsplash.com/photo-1550745165-9bc0b252726f?w=300&h=200&fit=crop' }
            ],
            stepRecord: [
                [
                    { name: '异常启动', timestamp: 1752761000 },
                    { name: '启动失败', timestamp: 1752762400 }
                ]
            ],
            stageList: [2, 6, 10], // 转场点索引
            correctDetail: {
                auto: {
                    firstFrameStatus: 0,
                    firstFrameIndex: 3,
                    firstFrameTimestamp: 1752761300,
                    lastFrameStatus: 0,
                    lastFrameIndex: 14,
                    lastFrameTimestamp: 1752762400
                },
                manual: {
                    firstFrameStatus: 0,
                    firstFrameIndex: 3,
                    firstFrameTimestamp: 1752761300,
                    lastFrameStatus: 0,
                    lastFrameIndex: 14,
                    lastFrameTimestamp: 1752762400
                }
            },
            isValid: 1, // 已废弃
            invalidComment: '启动异常，数据无效',
            updateUser: '<EMAIL>',
            updateTime: 1752762500
        },
        {
            recordSceneId: 126,
            isUpload: 1,
            frameList: [
                { timestamp: 1752760000, frame: 'https://images.unsplash.com/photo-1607706189992-eae578626c86?w=300&h=200&fit=crop' },
                { timestamp: 1752760080, frame: 'https://images.unsplash.com/photo-1593720213428-28a5b9e94613?w=300&h=200&fit=crop' },
                { timestamp: 1752760160, frame: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=300&h=200&fit=crop' }, // 快速首帧
                { timestamp: 1752760240, frame: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop' },
                { timestamp: 1752760320, frame: 'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=300&h=200&fit=crop' },
                { timestamp: 1752760400, frame: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=300&h=200&fit=crop' },
                { timestamp: 1752760480, frame: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=300&h=200&fit=crop' },
                { timestamp: 1752760560, frame: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=300&h=200&fit=crop' },
                { timestamp: 1752760640, frame: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=300&h=200&fit=crop' },
                { timestamp: 1752760720, frame: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop' },
                { timestamp: 1752760800, frame: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop' },
                { timestamp: 1752760880, frame: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=200&fit=crop' }, // 快速尾帧
            ],
            stepRecord: [
                [
                    { name: '热启动', timestamp: 1752760000 },
                    { name: '界面快速显示', timestamp: 1752760160 }
                ],
                [
                    { name: '快速加载完成', timestamp: 1752760880 }
                ]
            ],
            stageList: [1, 4, 8], // 转场点索引
            correctDetail: {
                auto: {
                    firstFrameStatus: 0,
                    firstFrameIndex: 2, // 快速首帧
                    firstFrameTimestamp: 1752760160,
                    lastFrameStatus: 0,
                    lastFrameIndex: 11, // 快速尾帧
                    lastFrameTimestamp: 1752760880
                },
                manual: {
                    firstFrameStatus: 1, // 已人工确认
                    firstFrameIndex: 2,
                    firstFrameTimestamp: 1752760160,
                    lastFrameStatus: 1, // 已人工确认
                    lastFrameIndex: 11,
                    lastFrameTimestamp: 1752760880
                }
            },
            isValid: 0,
            invalidComment: '',
            updateUser: '<EMAIL>',
            updateTime: 1752761000
        }
    ];

    return (
        <>
            <div className={styles.taskPage}>
                <Operate
                    planId={curTask?.planId}
                    caseNodeOptions={caseNodeOptions}
                    setCurCaseNodeId={setCurCaseNodeId}
                    setCurSceneId={setCurSceneId}
                    sceneListOptions={sceneListOptions}
                    recordList={recordList}
                />
                {/* <CalibrateTable recordList={recordList} /> */}
            </div>
        </>
    );
};

export default FramePage;
