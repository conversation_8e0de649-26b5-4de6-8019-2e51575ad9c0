cmd_Release/obj.target/sqlite3/gen/sqlite-autoconf-3440200/sqlite3.o := cc -o Release/obj.target/sqlite3/gen/sqlite-autoconf-3440200/sqlite3.o Release/obj/gen/sqlite-autoconf-3440200/sqlite3.c '-DNODE_GYP_MODULE_NAME=sqlite3' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-D_REENTRANT=1' '-DSQLITE_THREADSAFE=1' '-DHAVE_USLEEP=1' '-DSQLITE_ENABLE_FTS3' '-DSQLITE_ENABLE_FTS4' '-DSQLITE_ENABLE_FTS5' '-DSQLITE_ENABLE_RTREE' '-DSQLITE_ENABLE_DBSTAT_VTAB=1' '-DSQLITE_ENABLE_MATH_FUNCTIONS' '-DNDEBUG' -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/src -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/v8/include -I./Release/obj/gen/sqlite-autoconf-3440200  -O3 -mmacosx-version-min=10.15 -arch x86_64 -Wall -Wendif-labels -W -Wno-unused-parameter -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/sqlite3/gen/sqlite-autoconf-3440200/sqlite3.o.d.raw   -c
Release/obj.target/sqlite3/gen/sqlite-autoconf-3440200/sqlite3.o: \
  Release/obj/gen/sqlite-autoconf-3440200/sqlite3.c
Release/obj/gen/sqlite-autoconf-3440200/sqlite3.c:
