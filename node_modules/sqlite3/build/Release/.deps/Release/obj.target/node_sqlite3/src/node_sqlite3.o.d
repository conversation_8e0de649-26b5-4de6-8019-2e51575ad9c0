cmd_Release/obj.target/node_sqlite3/src/node_sqlite3.o := c++ -o Release/obj.target/node_sqlite3/src/node_sqlite3.o ../src/node_sqlite3.cc '-DNODE_GYP_MODULE_NAME=node_sqlite3' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DNAPI_VERSION=9' '-DNAPI_DISABLE_CPP_EXCEPTIONS=1' '-DSQLITE_THREADSAFE=1' '-DHAVE_USLEEP=1' '-DSQLITE_ENABLE_FTS3' '-DSQLITE_ENABLE_FTS4' '-DSQLITE_ENABLE_FTS5' '-DSQLITE_ENABLE_RTREE' '-DSQLITE_ENABLE_DBSTAT_VTAB=1' '-DSQLITE_ENABLE_MATH_FUNCTIONS' '-DBUILDING_NODE_EXTENSION' '-DNDEBUG' -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/src -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/v8/include -I/Users/<USER>/Desktop/baidu/baidu/node_modules/node-addon-api -I./Release/obj/gen/sqlite-autoconf-3440200  -O3 -mmacosx-version-min=10.7 -arch x86_64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++17 -stdlib=libc++ -fno-rtti -fvisibility-inlines-hidden -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/node_sqlite3/src/node_sqlite3.o.d.raw   -c
Release/obj.target/node_sqlite3/src/node_sqlite3.o: \
  ../src/node_sqlite3.cc \
  Release/obj/gen/sqlite-autoconf-3440200/sqlite3.h ../src/macros.h \
  /Users/<USER>/Desktop/baidu/baidu/node_modules/node-addon-api/napi.h \
  /Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/node_api_types.h \
  /Users/<USER>/Desktop/baidu/baidu/node_modules/node-addon-api/napi-inl.h \
  /Users/<USER>/Desktop/baidu/baidu/node_modules/node-addon-api/napi-inl.deprecated.h \
  ../src/database.h ../src/async.h \
  /Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv.h \
  /Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv/errno.h \
  /Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv/version.h \
  /Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv/unix.h \
  /Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv/threadpool.h \
  /Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv/darwin.h \
  ../src/threading.h ../src/statement.h ../src/backup.h
../src/node_sqlite3.cc:
Release/obj/gen/sqlite-autoconf-3440200/sqlite3.h:
../src/macros.h:
/Users/<USER>/Desktop/baidu/baidu/node_modules/node-addon-api/napi.h:
/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/node_api_types.h:
/Users/<USER>/Desktop/baidu/baidu/node_modules/node-addon-api/napi-inl.h:
/Users/<USER>/Desktop/baidu/baidu/node_modules/node-addon-api/napi-inl.deprecated.h:
../src/database.h:
../src/async.h:
/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv.h:
/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv/errno.h:
/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv/version.h:
/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv/unix.h:
/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv/threadpool.h:
/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node/uv/darwin.h:
../src/threading.h:
../src/statement.h:
../src/backup.h:
