cmd_Release/obj.target/nothing/../node-addon-api/nothing.o := cc -o Release/obj.target/nothing/../node-addon-api/nothing.o ../../node-addon-api/nothing.c '-DNODE_GYP_MODULE_NAME=nothing' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/include/node -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/src -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/18.17.1/deps/v8/include  -O3 -gdwarf-2 -mmacosx-version-min=10.15 -arch x86_64 -Wall -Wendif-labels -W -Wno-unused-parameter -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/nothing/../node-addon-api/nothing.o.d.raw   -c
Release/obj.target/nothing/../node-addon-api/nothing.o: \
  ../../node-addon-api/nothing.c
../../node-addon-api/nothing.c:
